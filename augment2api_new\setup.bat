@echo off

echo ===================================
echo   Augment2Api New Setup Script
echo ===================================
echo.

echo This script will help you set up environment variables.
echo Please enter the required values:
echo.

set /p TENANT_URL_INPUT="Enter TENANT_URL (e.g., https://your-tenant.augmentcode.com/api/v1/): "
set /p ACCESS_TOKEN_INPUT="Enter ACCESS_TOKEN: "
set /p AUTH_TOKEN_INPUT="Enter AUTH_TOKEN (optional, press Enter to skip): "
set /p PORT_INPUT="Enter PORT (default: 27080, press Enter to use default): "

echo.
echo Setting environment variables...

if not "%TENANT_URL_INPUT%"=="" (
    set TENANT_URL=%TENANT_URL_INPUT%
    echo OK: TENANT_URL set
) else (
    echo ERROR: TENANT_URL is required
    pause
    exit /b 1
)

if not "%ACCESS_TOKEN_INPUT%"=="" (
    set ACCESS_TOKEN=%ACCESS_TOKEN_INPUT%
    echo OK: ACCESS_TOKEN set
) else (
    echo ERROR: ACCESS_TOKEN is required
    pause
    exit /b 1
)

if not "%AUTH_TOKEN_INPUT%"=="" (
    set AUTH_TOKEN=%AUTH_TOKEN_INPUT%
    echo OK: AUTH_TOKEN set
) else (
    set AUTH_TOKEN=123456
    echo OK: AUTH_TOKEN set to default (123456)
)

if not "%PORT_INPUT%"=="" (
    set PORT=%PORT_INPUT%
    echo OK: PORT set to %PORT%
) else (
    set PORT=27080
    echo OK: PORT set to default (27080)
)

echo.
echo ===================================
echo Environment variables configured:
echo ===================================
echo TENANT_URL: %TENANT_URL%
echo ACCESS_TOKEN: [HIDDEN]
echo AUTH_TOKEN: [HIDDEN]
echo PORT: %PORT%
echo ===================================
echo.

echo Starting Augment2Api New...
call start.bat
