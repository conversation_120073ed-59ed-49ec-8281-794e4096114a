package api

// AnthropicUsage 表示Anthropic API使用统计结构
type AnthropicUsage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
}

// ToOpenAIUsage 转换为OpenAI格式的Usage
func (u *AnthropicUsage) ToOpenAIUsage() Usage {
	return Usage{
		PromptTokens:     u.InputTokens,
		CompletionTokens: u.OutputTokens,
		TotalTokens:      u.InputTokens + u.OutputTokens,
	}
}

// ToolSpec 表示工具规范的通用接口
type ToolSpec interface {
	GetName() string
	GetDescription() string
	GetInputSchema() map[string]any
}

// BaseTool 通用工具的基础结构
type BaseTool struct {
	Name        string         `json:"name"`
	Description string         `json:"description"`
	InputSchema map[string]any `json:"input_schema,omitempty"`
}

// GetName 实现ToolSpec接口
func (t *BaseTool) GetName() string {
	return t.Name
}

// GetDescription 实现ToolSpec接口
func (t *BaseTool) GetDescription() string {
	return t.Description
}

// GetInputSchema 实现ToolSpec接口
func (t *BaseTool) GetInputSchema() map[string]any {
	return t.InputSchema
}

// ToAnthropicTool 转换为Anthropic工具格式
func (t *BaseTool) ToAnthropicTool() AnthropicTool {
	return AnthropicTool{
		Name:        t.Name,
		Description: t.Description,
		InputSchema: t.InputSchema,
	}
}

// ToOpenAITool 转换为OpenAI工具格式
func (t *BaseTool) ToOpenAITool() OpenAITool {
	return OpenAITool{
		Type: "function",
		Function: OpenAIFunction{
			Name:        t.Name,
			Description: t.Description,
			Parameters:  t.InputSchema,
		},
	}
}

// AnthropicModel 表示Anthropic格式的模型信息
type AnthropicModel struct {
	ID          string `json:"id"`
	Object      string `json:"object"`
	Created     int64  `json:"created"`
	OwnedBy     string `json:"owned_by"`
	DisplayName string `json:"display_name"`
	Type        string `json:"type"`
	MaxTokens   int    `json:"max_tokens"`
}

// AnthropicModelsResponse 表示Anthropic格式的模型列表响应
type AnthropicModelsResponse struct {
	Object string           `json:"object"`
	Data   []AnthropicModel `json:"data"`
}
