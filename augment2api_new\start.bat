@echo off

echo ===================================
echo     Augment2Api New v2.0.0
echo ===================================

echo Checking environment variables...

set missing_vars=0

if "%TENANT_URL%"=="" (
    echo ERROR: Environment variable TENANT_URL is not set
    set /a missing_vars+=1
) else (
    echo OK: TENANT_URL is set
)

if "%ACCESS_TOKEN%"=="" (
    echo ERROR: Environment variable ACCESS_TOKEN is not set
    set /a missing_vars+=1
) else (
    echo OK: ACCESS_TOKEN is set
)

echo.
echo Optional environment variables:
if "%AUTH_TOKEN%"=="" (
    echo AUTH_TOKEN: not set (will use default)
) else (
    echo AUTH_TOKEN: set
)

if "%PORT%"=="" (
    echo PORT: 27080 (default)
) else (
    echo PORT: %PORT%
)

if "%ROUTE_PREFIX%"=="" (
    echo ROUTE_PREFIX: /v1 (default)
) else (
    echo ROUTE_PREFIX: %ROUTE_PREFIX%
)

if "%PROXY_URL%"=="" (
    echo PROXY_URL: not set
) else (
    echo PROXY_URL: %PROXY_URL%
)

if %missing_vars% gtr 0 (
    echo.
    echo ERROR: Missing %missing_vars% required environment variables
    echo.
    echo Please set the following environment variables:
    echo set TENANT_URL=your_augment_tenant_url
    echo set ACCESS_TOKEN=your_augment_access_token
    echo set AUTH_TOKEN=your_api_auth_token  (optional)
    echo set PORT=27080  (optional)
    echo.
    echo Then run this script again
    pause
    exit /b 1
)

echo.
echo OK: Environment variables check passed
echo.

if not exist "augment2api_new.exe" (
    echo ERROR: Executable not found, compiling...
    go build -o augment2api_new.exe
    if errorlevel 1 (
        echo ERROR: Compilation failed
        pause
        exit /b 1
    )
    echo OK: Compilation successful
)

echo Starting server...
echo ===================================

augment2api_new.exe

pause
