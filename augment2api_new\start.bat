@echo off
chcp 65001 >nul

echo ===================================
echo     Augment2Api New v2.0.0
echo ===================================

echo 检查环境变量...

set missing_vars=0

if "%TENANT_URL%"=="" (
    echo ❌ 错误: 环境变量 TENANT_URL 未设置
    set /a missing_vars+=1
) else (
    echo ✅ TENANT_URL: %TENANT_URL:~0,20%...
)

if "%ACCESS_TOKEN%"=="" (
    echo ❌ 错误: 环境变量 ACCESS_TOKEN 未设置
    set /a missing_vars+=1
) else (
    echo ✅ ACCESS_TOKEN: %ACCESS_TOKEN:~0,20%...
)

echo.
echo 可选环境变量:
if "%AUTH_TOKEN%"=="" (
    echo AUTH_TOKEN: 未设置（将使用默认值）
) else (
    echo AUTH_TOKEN: %AUTH_TOKEN:~0,20%...
)

if "%PORT%"=="" (
    echo PORT: 27080 (默认)
) else (
    echo PORT: %PORT%
)

if "%ROUTE_PREFIX%"=="" (
    echo ROUTE_PREFIX: /v1 (默认)
) else (
    echo ROUTE_PREFIX: %ROUTE_PREFIX%
)

if "%PROXY_URL%"=="" (
    echo PROXY_URL: 未设置
) else (
    echo PROXY_URL: %PROXY_URL%
)

if %missing_vars% gtr 0 (
    echo.
    echo ❌ 缺少 %missing_vars% 个必需的环境变量
    echo.
    echo 请设置以下环境变量:
    echo set TENANT_URL=your_augment_tenant_url
    echo set ACCESS_TOKEN=your_augment_access_token
    echo set AUTH_TOKEN=your_api_auth_token  ^(可选^)
    echo set PORT=27080  ^(可选^)
    echo.
    echo 然后重新运行此脚本
    pause
    exit /b 1
)

echo.
echo ✅ 环境变量检查通过
echo.

if not exist "augment2api_new.exe" (
    echo ❌ 可执行文件不存在，正在编译...
    go build -o augment2api_new.exe
    if errorlevel 1 (
        echo ❌ 编译失败
        pause
        exit /b 1
    )
    echo ✅ 编译成功
)

echo 启动服务器...
echo ===================================

augment2api_new.exe

pause
