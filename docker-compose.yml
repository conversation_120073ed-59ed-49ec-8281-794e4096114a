version: '3.8'

services:
  redis:
    image: redis:alpine
    restart: always
    volumes:
      - redis_data:/data
    command: redis-server --requirepass ${REDIS_PASSWORD:-yourpassword}
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-yourpassword}", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
    environment:
      - TZ=Asia/Shanghai
      - REDIS_PASSWORD=${REDIS_PASSWORD:-yourpassword}

  augment2api:
    build: .
    restart: always
    ports:
      - "27080:27080"
    environment:
      - REDIS_CONN_STRING=redis://default:${REDIS_PASSWORD:-yourpassword}@redis:6379
      - ACCESS_PWD=${ACCESS_PWD:-your-access-password}
      - AUTH_TOKEN=${AUTH_TOKEN:-your-auth-token}
      - TZ=Asia/Shanghai
    depends_on:
      redis:
        condition: service_healthy

volumes:
  redis_data:

networks:
  default:
    driver: bridge 
