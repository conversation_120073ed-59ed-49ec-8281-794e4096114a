package server

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"augment2api_new/config"
	"augment2api_new/converter"
	"augment2api_new/types"
	"augment2api_new/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// 全局变量
var (
	accessToken string
	tenantURL   string
	logger      *logrus.Logger
)

func init() {
	logger = logrus.New()
	logger.SetLevel(logrus.InfoLevel)
}

// SetAuthInfo 设置认证信息
func SetAuthInfo(token, tenant string) {
	accessToken = token
	tenantURL = tenant
}

// GetAuthInfo 获取认证信息
func GetAuthInfo() (string, string) {
	return accessToken, tenantURL
}

// AnthropicMessagesHandler 处理Anthropic Messages API请求
func AnthropicMessagesHandler(c *gin.Context) {
	var req types.AnthropicRequest
	if err := c.<PERSON>(&req); err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"type": "error",
			"error": map[string]interface{}{
				"type":    "invalid_request_error",
				"message": "Invalid request format",
			},
		})
		return
	}

	// 验证必需字段
	if req.Model == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"type": "error",
			"error": map[string]interface{}{
				"type":    "invalid_request_error",
				"message": "Missing required field: model",
			},
		})
		return
	}

	if req.MaxTokens <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"type": "error",
			"error": map[string]interface{}{
				"type":    "invalid_request_error",
				"message": "Missing required field: max_tokens",
			},
		})
		return
	}

	if len(req.Messages) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"type": "error",
			"error": map[string]interface{}{
				"type":    "invalid_request_error",
				"message": "Missing required field: messages",
			},
		})
		return
	}

	// 转换为内部格式
	augmentReq := converter.ConvertAnthropicToAugment(req)

	if req.Stream {
		handleAnthropicStream(c, augmentReq, req)
	} else {
		handleAnthropicNonStream(c, augmentReq, req)
	}
}

// handleAnthropicStream 处理流式请求
func handleAnthropicStream(c *gin.Context, augmentReq types.AugmentRequest, originalReq types.AnthropicRequest) {
	// 设置SSE头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")

	messageId := fmt.Sprintf("msg_%d", time.Now().UnixNano())

	// 发送 message_start 事件
	startEvent := map[string]interface{}{
		"type": "message_start",
		"message": map[string]interface{}{
			"id":            messageId,
			"type":          "message",
			"role":          "assistant",
			"content":       []interface{}{},
			"model":         originalReq.Model,
			"stop_reason":   nil,
			"stop_sequence": nil,
			"usage": map[string]interface{}{
				"input_tokens":  0, // 需要实际计算
				"output_tokens": 0,
			},
		},
	}
	sendSSEEvent(c, startEvent)

	// 发送 content_block_start 事件
	blockStartEvent := map[string]interface{}{
		"type":  "content_block_start",
		"index": 0,
		"content_block": map[string]interface{}{
			"type": "text",
			"text": "",
		},
	}
	sendSSEEvent(c, blockStartEvent)

	// 调用Augment API流式处理
	callAugmentStreamAPI(c, augmentReq, originalReq.Model, messageId)
}

// handleAnthropicNonStream 处理非流式请求
func handleAnthropicNonStream(c *gin.Context, augmentReq types.AugmentRequest, originalReq types.AnthropicRequest) {
	// 调用Augment API
	fullResponse, err := callAugmentAPI(augmentReq, originalReq.Model)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"type": "error",
			"error": map[string]interface{}{
				"type":    "api_error",
				"message": err.Error(),
			},
		})
		return
	}

	// 构造标准Anthropic响应
	messageId := fmt.Sprintf("msg_%d", time.Now().UnixNano())
	response := converter.ConvertAugmentToAnthropic(fullResponse, originalReq.Model, messageId)

	c.JSON(http.StatusOK, response)
}

// OpenAIChatCompletionsHandler 处理OpenAI Chat Completions API请求
func OpenAIChatCompletionsHandler(c *gin.Context) {
	var req types.OpenAIRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// 转换为Anthropic格式
	anthropicReq := converter.ConvertOpenAIToAnthropic(req)

	// 转换为Augment格式
	augmentReq := converter.ConvertAnthropicToAugment(anthropicReq)

	stream := false
	if req.Stream != nil {
		stream = *req.Stream
	}

	if stream {
		handleOpenAIStream(c, augmentReq, req)
	} else {
		handleOpenAINonStream(c, augmentReq, req)
	}
}

// ModelsHandler 处理模型列表请求
func ModelsHandler(c *gin.Context) {
	response := types.ModelsResponse{
		Object: "list",
		Data: []types.ModelObject{
			{
				ID:      "claude-3-5-sonnet-20241022",
				Object:  "model",
				Created: 1708387200,
				OwnedBy: "anthropic",
			},
			{
				ID:      "claude-3-5-haiku-20241022",
				Object:  "model",
				Created: 1708387200,
				OwnedBy: "anthropic",
			},
			{
				ID:      "claude-3-opus-20240229",
				Object:  "model",
				Created: 1708387200,
				OwnedBy: "anthropic",
			},
			{
				ID:      "gpt-4",
				Object:  "model",
				Created: 1708387200,
				OwnedBy: "openai",
			},
			{
				ID:      "gpt-4-turbo",
				Object:  "model",
				Created: 1708387200,
				OwnedBy: "openai",
			},
			{
				ID:      "gpt-3.5-turbo",
				Object:  "model",
				Created: 1708387200,
				OwnedBy: "openai",
			},
		},
	}

	c.JSON(http.StatusOK, response)
}

// sendSSEEvent 发送SSE事件
func sendSSEEvent(c *gin.Context, data interface{}) {
	jsonData, _ := json.Marshal(data)
	fmt.Fprintf(c.Writer, "data: %s\n\n", jsonData)
	c.Writer.Flush()
}

// buildAugmentRequest 构建Augment API请求
func buildAugmentRequest(augmentReq types.AugmentRequest, token, tenant string, isStream bool) (*http.Request, error) {
	augmentReqBody, err := json.Marshal(augmentReq)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	requestURL := tenant + "chat-stream"
	req, err := http.NewRequest("POST", requestURL, bytes.NewReader(augmentReqBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 提取主机部分
	parsedURL, err := url.Parse(tenant)
	if err != nil {
		return nil, fmt.Errorf("解析租户URL失败: %v", err)
	}
	hostName := parsedURL.Host

	// 设置请求头
	req.Header.Set("Host", hostName)
	req.Header.Set("Content-Length", fmt.Sprintf("%d", len(augmentReqBody)))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", config.AppConfig.UserAgent)
	req.Header.Set("x-api-version", "2")
	req.Header.Set("x-request-id", uuid.New().String())
	req.Header.Set("x-request-session-id", uuid.New().String())

	return req, nil
}

// callAugmentAPI 调用Augment API（非流式）
func callAugmentAPI(augmentReq types.AugmentRequest, model string) (string, error) {
	token, tenant := GetAuthInfo()
	if token == "" || tenant == "" {
		return "", fmt.Errorf("无可用Token")
	}

	req, err := buildAugmentRequest(augmentReq, token, tenant, false)
	if err != nil {
		return "", err
	}

	client := utils.CreateHTTPClient(config.AppConfig.ProxyURL)
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("Augment response error: %s", string(body))
	}

	// 读取完整响应
	reader := bufio.NewReader(resp.Body)
	var fullText string

	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			return "", fmt.Errorf("读取响应失败: %v", err)
		}

		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 解析JSON响应
		var augmentResp types.AugmentResponse
		if err := json.Unmarshal([]byte(line), &augmentResp); err != nil {
			continue
		}

		fullText += augmentResp.Text

		if augmentResp.Done {
			break
		}
	}

	return fullText, nil
}

// callAugmentStreamAPI 调用Augment API（流式）
func callAugmentStreamAPI(c *gin.Context, augmentReq types.AugmentRequest, model string, messageId string) {
	token, tenant := GetAuthInfo()
	if token == "" || tenant == "" {
		sendSSEEvent(c, map[string]interface{}{
			"type": "error",
			"error": map[string]interface{}{
				"type":    "authentication_error",
				"message": "无可用Token",
			},
		})
		return
	}

	req, err := buildAugmentRequest(augmentReq, token, tenant, true)
	if err != nil {
		sendSSEEvent(c, map[string]interface{}{
			"type": "error",
			"error": map[string]interface{}{
				"type":    "api_error",
				"message": err.Error(),
			},
		})
		return
	}

	client := utils.CreateHTTPClient(config.AppConfig.ProxyURL)
	resp, err := client.Do(req)
	if err != nil {
		sendSSEEvent(c, map[string]interface{}{
			"type": "error",
			"error": map[string]interface{}{
				"type":    "api_error",
				"message": fmt.Sprintf("请求失败: %v", err),
			},
		})
		return
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		sendSSEEvent(c, map[string]interface{}{
			"type": "error",
			"error": map[string]interface{}{
				"type":    "api_error",
				"message": fmt.Sprintf("Augment response error: %s", string(body)),
			},
		})
		return
	}

	reader := bufio.NewReader(resp.Body)
	var fullText string

	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			logger.WithFields(logrus.Fields{
				"error": err.Error(),
			}).Error("读取响应失败")
			break
		}

		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 解析JSON响应
		var augmentResp types.AugmentResponse
		if err := json.Unmarshal([]byte(line), &augmentResp); err != nil {
			continue
		}

		if augmentResp.Text != "" {
			fullText += augmentResp.Text

			// 发送 content_block_delta 事件
			deltaEvent := map[string]interface{}{
				"type":  "content_block_delta",
				"index": 0,
				"delta": map[string]interface{}{
					"type": "text_delta",
					"text": augmentResp.Text,
				},
			}
			sendSSEEvent(c, deltaEvent)
		}

		if augmentResp.Done {
			break
		}
	}

	// 发送结束事件
	blockStopEvent := map[string]interface{}{
		"type":  "content_block_stop",
		"index": 0,
	}
	sendSSEEvent(c, blockStopEvent)

	finalEvent := map[string]interface{}{
		"type": "message_delta",
		"delta": map[string]interface{}{
			"stop_reason":   "end_turn",
			"stop_sequence": nil,
		},
		"usage": map[string]interface{}{
			"output_tokens": len(fullText) / 4, // 简单估算
		},
	}
	sendSSEEvent(c, finalEvent)

	stopEvent := map[string]interface{}{
		"type": "message_stop",
	}
	sendSSEEvent(c, stopEvent)
}

// handleOpenAIStream 处理OpenAI流式请求
func handleOpenAIStream(c *gin.Context, augmentReq types.AugmentRequest, originalReq types.OpenAIRequest) {
	// 设置SSE头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")

	responseID := fmt.Sprintf("chatcmpl-%d", time.Now().Unix())

	token, tenant := GetAuthInfo()
	if token == "" || tenant == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "无可用Token"})
		return
	}

	req, err := buildAugmentRequest(augmentReq, token, tenant, true)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "构建请求失败"})
		return
	}

	client := utils.CreateHTTPClient(config.AppConfig.ProxyURL)
	resp, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "请求失败"})
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		c.JSON(resp.StatusCode, gin.H{"error": fmt.Sprintf("Augment response error: %s", string(body))})
		return
	}

	reader := bufio.NewReader(resp.Body)

	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			break
		}

		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		var augmentResp types.AugmentResponse
		if err := json.Unmarshal([]byte(line), &augmentResp); err != nil {
			continue
		}

		if augmentResp.Text != "" {
			// 创建OpenAI兼容的流式响应
			streamResp := types.OpenAIStreamResponse{
				ID:      responseID,
				Object:  "chat.completion.chunk",
				Created: time.Now().Unix(),
				Model:   originalReq.Model,
				Choices: []types.OpenAIStreamChoice{
					{
						Index: 0,
						Delta: struct {
							Role    string `json:"role,omitempty"`
							Content string `json:"content,omitempty"`
						}{
							Content: augmentResp.Text,
						},
						FinishReason: nil,
					},
				},
			}

			jsonData, _ := json.Marshal(streamResp)
			fmt.Fprintf(c.Writer, "data: %s\n\n", jsonData)
			c.Writer.Flush()
		}

		if augmentResp.Done {
			// 发送结束标记
			finishResp := types.OpenAIStreamResponse{
				ID:      responseID,
				Object:  "chat.completion.chunk",
				Created: time.Now().Unix(),
				Model:   originalReq.Model,
				Choices: []types.OpenAIStreamChoice{
					{
						Index:        0,
						Delta:        struct {
							Role    string `json:"role,omitempty"`
							Content string `json:"content,omitempty"`
						}{},
						FinishReason: stringPtr("stop"),
					},
				},
			}

			jsonData, _ := json.Marshal(finishResp)
			fmt.Fprintf(c.Writer, "data: %s\n\n", jsonData)
			fmt.Fprintf(c.Writer, "data: [DONE]\n\n")
			c.Writer.Flush()
			break
		}
	}
}

// handleOpenAINonStream 处理OpenAI非流式请求
func handleOpenAINonStream(c *gin.Context, augmentReq types.AugmentRequest, originalReq types.OpenAIRequest) {
	fullResponse, err := callAugmentAPI(augmentReq, originalReq.Model)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 构造OpenAI格式响应
	responseID := fmt.Sprintf("chatcmpl-%d", time.Now().Unix())

	response := types.OpenAIResponse{
		ID:      responseID,
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   originalReq.Model,
		Choices: []types.OpenAIChoice{
			{
				Index: 0,
				Message: types.OpenAIMessage{
					Role:    "assistant",
					Content: fullResponse,
				},
				FinishReason: "stop",
			},
		},
		Usage: types.Usage{
			PromptTokens:     len(augmentReq.Message) / 4, // 简单估算
			CompletionTokens: len(fullResponse) / 4,
			TotalTokens:      (len(augmentReq.Message) + len(fullResponse)) / 4,
		},
	}

	c.JSON(http.StatusOK, response)
}

// stringPtr 返回字符串指针
func stringPtr(s string) *string {
	return &s
}
