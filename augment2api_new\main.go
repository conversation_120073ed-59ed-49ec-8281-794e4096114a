package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"

	"augment2api_new/config"
	"augment2api_new/server"

	"github.com/gin-gonic/gin"
)

func main() {
	// 初始化配置
	if err := config.InitConfig(); err != nil {
		log.Fatalf("配置初始化失败: %v", err)
	}

	// 设置认证信息
	server.SetAuthInfo(config.AppConfig.AccessToken, config.AppConfig.TenantURL)

	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	// 创建路由
	r := gin.New()

	// 添加中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())
	r.Use(corsMiddleware())
	r.Use(authMiddleware())

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"version": config.Version,
		})
	})

	// 根路径
	r.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "Augment2Api New is running",
			"version": config.Version,
		})
	})

	// 设置路由前缀
	prefix := config.AppConfig.RoutePrefix
	if prefix == "" {
		prefix = "/v1"
	}
	if !strings.HasPrefix(prefix, "/") {
		prefix = "/" + prefix
	}

	v1 := r.Group(prefix)
	{
		// Anthropic Claude API兼容接口
		v1.POST("/messages", server.AnthropicMessagesHandler)

		// OpenAI API兼容接口
		v1.POST("/chat/completions", server.OpenAIChatCompletionsHandler)

		// 模型列表接口
		v1.GET("/models", server.ModelsHandler)
	}

	// 启动服务器
	port := config.AppConfig.Port
	if port == "" {
		port = "27080"
	}

	log.Printf("服务器启动在端口 %s", port)
	log.Printf("Anthropic API: http://localhost:%s%s/messages", port, prefix)
	log.Printf("OpenAI API: http://localhost:%s%s/chat/completions", port, prefix)
	log.Printf("Models API: http://localhost:%s%s/models", port, prefix)

	if err := r.Run(":" + port); err != nil {
		log.Fatalf("服务器启动失败: %v", err)
	}
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// authMiddleware 认证中间件
func authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过健康检查和根路径
		if c.Request.URL.Path == "/health" || c.Request.URL.Path == "/" {
			c.Next()
			return
		}

		// 检查Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"type": "error",
				"error": map[string]interface{}{
					"type":    "authentication_error",
					"message": "Missing Authorization header",
				},
			})
			c.Abort()
			return
		}

		// 验证Bearer token
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.JSON(http.StatusUnauthorized, gin.H{
				"type": "error",
				"error": map[string]interface{}{
					"type":    "authentication_error",
					"message": "Invalid Authorization header format",
				},
			})
			c.Abort()
			return
		}

		token := strings.TrimPrefix(authHeader, "Bearer ")
		
		// 简单的token验证（可以根据需要扩展）
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"type": "error",
				"error": map[string]interface{}{
					"type":    "authentication_error",
					"message": "Invalid token",
				},
			})
			c.Abort()
			return
		}

		// 如果配置了AUTH_TOKEN，则验证
		if config.AppConfig.AuthToken != "" && token != config.AppConfig.AuthToken {
			c.JSON(http.StatusUnauthorized, gin.H{
				"type": "error",
				"error": map[string]interface{}{
					"type":    "authentication_error",
					"message": "Invalid token",
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

func init() {
	// 检查必需的环境变量
	requiredEnvs := []string{"TENANT_URL", "ACCESS_TOKEN"}
	missing := []string{}

	for _, env := range requiredEnvs {
		if os.Getenv(env) == "" {
			missing = append(missing, env)
		}
	}

	if len(missing) > 0 {
		fmt.Printf("错误: 缺少必需的环境变量: %s\n", strings.Join(missing, ", "))
		fmt.Println("\n请设置以下环境变量:")
		fmt.Println("export TENANT_URL=your_augment_tenant_url")
		fmt.Println("export ACCESS_TOKEN=your_augment_access_token")
		fmt.Println("export AUTH_TOKEN=your_api_auth_token  # 可选")
		fmt.Println("export PORT=27080  # 可选，默认27080")
		os.Exit(1)
	}
}
