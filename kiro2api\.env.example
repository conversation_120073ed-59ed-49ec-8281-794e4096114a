# ============================================================================
# 必需的环境变量
# ============================================================================

# AWS RefreshToken - 必需设置，程序启动时会检查此环境变量
# 可以从 ~/.aws/sso/cache/kiro-auth-token.json 中获取
AWS_REFRESHTOKEN=your_refresh_token_here

# ============================================================================
# 可选的环境变量
# ============================================================================

# 端口配置 (默认: 8080)
PORT=8080

# 客户端认证token (默认: 123456)
KIRO_CLIENT_TOKEN=123456

# Gin 模式: debug, release, test
GIN_MODE=release

# 日志级别: debug, info, warn, error
LOG_LEVEL=info

# 日志格式: text, json
LOG_FORMAT=json

# ============================================================================
# 超时配置
# ============================================================================

# HTTP客户端请求超时配置（分钟）
REQUEST_TIMEOUT_MINUTES=15              # 复杂请求超时 (默认: 15分钟)
SIMPLE_REQUEST_TIMEOUT_MINUTES=2        # 简单请求超时 (默认: 2分钟)

# HTTP服务器超时配置（分钟）
SERVER_READ_TIMEOUT_MINUTES=16          # 服务器读取超时 (默认: 16分钟)
SERVER_WRITE_TIMEOUT_MINUTES=16         # 服务器写入超时 (默认: 16分钟)