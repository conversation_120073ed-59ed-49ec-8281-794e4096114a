package converter

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"augment2api_new/types"

	"github.com/google/uuid"
)

// ConvertOpenAIToAnthropic 将OpenAI请求转换为Anthropic请求
func ConvertOpenAIToAnthropic(openaiReq types.OpenAIRequest) types.AnthropicRequest {
	var anthropicMessages []types.AnthropicRequestMessage

	// 转换消息
	for _, msg := range openaiReq.Messages {
		anthropicMsg := types.AnthropicRequestMessage{
			Role:    msg.Role,
			Content: msg.Content,
		}
		anthropicMessages = append(anthropicMessages, anthropicMsg)
	}

	// 设置默认值
	maxTokens := 16384
	if openaiReq.MaxTokens != nil {
		maxTokens = *openaiReq.MaxTokens
	}

	// 为了增强兼容性，当stream未设置时默认为false（非流式响应）
	stream := false
	if openaiReq.Stream != nil {
		stream = *openaiReq.Stream
	}

	anthropicReq := types.AnthropicRequest{
		Model:     openaiReq.Model,
		MaxTokens: maxTokens,
		Messages:  anthropicMessages,
		Stream:    stream,
	}

	if openaiReq.Temperature != nil {
		anthropicReq.Temperature = openaiReq.Temperature
	}

	// 转换 tools
	if len(openaiReq.Tools) > 0 {
		anthropicTools, err := validateAndProcessTools(openaiReq.Tools)
		if err != nil {
			// 记录警告但不中断处理，允许部分工具失败
		}
		anthropicReq.Tools = anthropicTools
	}

	// 转换 tool_choice
	if openaiReq.ToolChoice != nil {
		anthropicReq.ToolChoice = convertOpenAIToolChoiceToAnthropic(openaiReq.ToolChoice)
	}

	return anthropicReq
}

// ConvertAnthropicToAugment 将Anthropic请求转换为Augment请求
func ConvertAnthropicToAugment(anthropicReq types.AnthropicRequest) types.AugmentRequest {
	augmentReq := types.AugmentRequest{
		Mode:           "CHAT",
		Prefix:         "",
		Suffix:         " ",
		Lang:           "HTML",
		Path:           "",
		UserGuideLines: "使用中文回答",
		ChatHistory:    make([]types.AugmentChatHistory, 0),
		Blobs: struct {
			CheckpointID string        `json:"checkpoint_id"`
			AddedBlobs   []interface{} `json:"added_blobs"`
			DeletedBlobs []interface{} `json:"deleted_blobs"`
		}{
			CheckpointID: generateCheckpointID(),
			AddedBlobs:   make([]interface{}, 0),
			DeletedBlobs: make([]interface{}, 0),
		},
		UserGuidedBlobs:   make([]interface{}, 0),
		ExternalSourceIds: make([]interface{}, 0),
		FeatureDetectionFlags: struct {
			SupportRawOutput bool `json:"support_raw_output"`
		}{
			SupportRawOutput: true,
		},
		ToolDefinitions: make([]types.ToolDefinition, 0),
		Nodes:           make([]types.Node, 0),
	}

	// 处理system消息
	if len(anthropicReq.System) > 0 {
		var systemContentBuilder strings.Builder
		for _, sysMsg := range anthropicReq.System {
			content := getMessageContent(sysMsg.Text)
			systemContentBuilder.WriteString(content)
			systemContentBuilder.WriteString("\n")
		}

		// 将system消息作为第一条对话添加到历史中
		systemHistory := types.AugmentChatHistory{
			RequestMessage: strings.TrimSpace(systemContentBuilder.String()),
			ResponseText:   "OK",
			RequestID:      generateRequestID(),
			RequestNodes:   make([]types.Node, 0),
			ResponseNodes: []types.Node{
				{
					ID:      0,
					Type:    0,
					Content: "OK",
				},
			},
		}
		augmentReq.ChatHistory = append(augmentReq.ChatHistory, systemHistory)
	}

	// 处理消息历史（除了最后一条消息）
	if len(anthropicReq.Messages) > 1 {
		for i := 0; i < len(anthropicReq.Messages)-1; i += 2 {
			if i+1 < len(anthropicReq.Messages) {
				userMsg := anthropicReq.Messages[i]
				assistantMsg := anthropicReq.Messages[i+1]

				if userMsg.Role == "user" && assistantMsg.Role == "assistant" {
					chatHistory := types.AugmentChatHistory{
						RequestMessage: getMessageContent(userMsg.Content),
						ResponseText:   getMessageContent(assistantMsg.Content),
						RequestID:      generateRequestID(),
						RequestNodes:   make([]types.Node, 0),
						ResponseNodes: []types.Node{
							{
								ID:      0,
								Type:    0,
								Content: getMessageContent(assistantMsg.Content),
							},
						},
					}
					augmentReq.ChatHistory = append(augmentReq.ChatHistory, chatHistory)
				}
			}
		}
	}

	// 设置当前消息（最后一条消息）
	if len(anthropicReq.Messages) > 0 {
		lastMsg := anthropicReq.Messages[len(anthropicReq.Messages)-1]
		augmentReq.Message = getMessageContent(lastMsg.Content)
	}

	// 处理工具定义
	if len(anthropicReq.Tools) > 0 {
		for _, tool := range anthropicReq.Tools {
			schemaJSON, _ := json.Marshal(tool.InputSchema)
			toolDef := types.ToolDefinition{
				Name:            tool.Name,
				Description:     tool.Description,
				InputSchemaJSON: string(schemaJSON),
				ToolSafety:      1,
			}
			augmentReq.ToolDefinitions = append(augmentReq.ToolDefinitions, toolDef)
		}
	}

	return augmentReq
}

// ConvertAugmentToAnthropic 将Augment响应转换为Anthropic响应
func ConvertAugmentToAnthropic(augmentText string, model string, messageId string) map[string]any {
	return map[string]any{
		"id":   messageId,
		"type": "message",
		"role": "assistant",
		"content": []map[string]any{
			{
				"type": "text",
				"text": augmentText,
			},
		},
		"model":        model,
		"stop_reason":  "end_turn",
		"stop_sequence": nil,
		"usage": map[string]any{
			"input_tokens":  estimateTokenCount(""),
			"output_tokens": estimateTokenCount(augmentText),
		},
	}
}

// ConvertAnthropicToOpenAI 将Anthropic响应转换为OpenAI响应
func ConvertAnthropicToOpenAI(anthropicResp map[string]any, model string, messageId string) types.OpenAIResponse {
	content := ""
	var toolCalls []types.OpenAIToolCall
	finishReason := "stop"

	// 处理content数组
	if contentArray, ok := anthropicResp["content"].([]any); ok && len(contentArray) > 0 {
		var textParts []string
		for _, block := range contentArray {
			if textBlock, ok := block.(map[string]any); ok {
				if blockType, ok := textBlock["type"].(string); ok {
					switch blockType {
					case "text":
						if text, ok := textBlock["text"].(string); ok {
							textParts = append(textParts, text)
						}
					case "tool_use":
						finishReason = "tool_calls"
						if toolUseId, ok := textBlock["id"].(string); ok {
							if toolName, ok := textBlock["name"].(string); ok {
								if input, ok := textBlock["input"]; ok {
									inputJson, _ := json.Marshal(input)
									toolCall := types.OpenAIToolCall{
										ID:   toolUseId,
										Type: "function",
										Function: types.OpenAIToolFunction{
											Name:      toolName,
											Arguments: string(inputJson),
										},
									}
									toolCalls = append(toolCalls, toolCall)
								}
							}
						}
					}
				}
			}
		}
		content = strings.Join(textParts, "")
	}

	// 计算token使用量
	promptTokens := 0
	completionTokens := estimateTokenCount(content)
	if usage, ok := anthropicResp["usage"].(map[string]any); ok {
		if inputTokens, ok := usage["input_tokens"].(int); ok {
			promptTokens = inputTokens
		}
		if outputTokens, ok := usage["output_tokens"].(int); ok {
			completionTokens = outputTokens
		}
	}

	message := types.OpenAIMessage{
		Role:    "assistant",
		Content: content,
	}

	// 只有当有tool_calls时才添加ToolCalls字段
	if len(toolCalls) > 0 {
		message.ToolCalls = toolCalls
	}

	return types.OpenAIResponse{
		ID:      messageId,
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   model,
		Choices: []types.OpenAIChoice{
			{
				Index:        0,
				Message:      message,
				FinishReason: finishReason,
			},
		},
		Usage: types.Usage{
			PromptTokens:     promptTokens,
			CompletionTokens: completionTokens,
			TotalTokens:      promptTokens + completionTokens,
		},
	}
}

// 辅助函数

// getMessageContent 提取消息内容
func getMessageContent(content interface{}) string {
	switch v := content.(type) {
	case string:
		return v
	case []interface{}:
		var result string
		for _, item := range v {
			if contentMap, ok := item.(map[string]interface{}); ok {
				if text, exists := contentMap["text"]; exists {
					if textStr, ok := text.(string); ok {
						result += textStr
					}
				}
			}
		}
		return result
	default:
		return ""
	}
}

// generateCheckpointID 生成检查点ID
func generateCheckpointID() string {
	return uuid.New().String()
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	return uuid.New().String()
}

// estimateTokenCount 估算token数量
func estimateTokenCount(text string) int {
	if text == "" {
		return 0
	}
	// 简单估算：平均4个字符为1个token
	return len(text) / 4
}

// validateAndProcessTools 验证和处理工具定义
func validateAndProcessTools(tools []types.OpenAITool) ([]types.AnthropicTool, error) {
	if len(tools) == 0 {
		return nil, nil
	}

	var anthropicTools []types.AnthropicTool
	var validationErrors []string

	for i, tool := range tools {
		if tool.Type != "function" {
			validationErrors = append(validationErrors, fmt.Sprintf("tool[%d]: 不支持的工具类型 '%s'，仅支持 'function'", i, tool.Type))
			continue
		}

		// 验证函数名称
		if tool.Function.Name == "" {
			validationErrors = append(validationErrors, fmt.Sprintf("tool[%d]: 函数名称不能为空", i))
			continue
		}

		// 验证参数schema
		if tool.Function.Parameters == nil {
			validationErrors = append(validationErrors, fmt.Sprintf("tool[%d]: 参数schema不能为空", i))
			continue
		}

		// 清理和验证参数
		cleanedParams, err := cleanAndValidateToolParameters(tool.Function.Parameters, tool.Function.Name)
		if err != nil {
			validationErrors = append(validationErrors, fmt.Sprintf("tool[%d] (%s): %v", i, tool.Function.Name, err))
			continue
		}

		anthropicTool := types.AnthropicTool{
			Name:        tool.Function.Name,
			Description: tool.Function.Description,
			InputSchema: cleanedParams,
		}
		anthropicTools = append(anthropicTools, anthropicTool)
	}

	if len(validationErrors) > 0 {
		return anthropicTools, fmt.Errorf("工具验证失败: %s", strings.Join(validationErrors, "; "))
	}

	return anthropicTools, nil
}

// cleanAndValidateToolParameters 清理和验证工具参数
func cleanAndValidateToolParameters(params map[string]any, _ string) (map[string]any, error) {
	if params == nil {
		return nil, fmt.Errorf("参数不能为nil")
	}

	// 深拷贝避免修改原始数据
	cleanedParams, _ := json.Marshal(params)
	var tempParams map[string]any
	if err := json.Unmarshal(cleanedParams, &tempParams); err != nil {
		return nil, fmt.Errorf("参数序列化失败: %v", err)
	}

	// 移除不支持的顶级字段
	delete(tempParams, "additionalProperties")
	delete(tempParams, "strict")
	delete(tempParams, "$schema")
	delete(tempParams, "$id")
	delete(tempParams, "$ref")
	delete(tempParams, "definitions")
	delete(tempParams, "$defs")

	// 验证必需的字段
	if schemaType, exists := tempParams["type"]; exists {
		if typeStr, ok := schemaType.(string); ok && typeStr == "object" {
			// 对象类型应该有properties字段
			if _, hasProps := tempParams["properties"]; !hasProps {
				return nil, fmt.Errorf("对象类型缺少properties字段")
			}
		}
	}

	return tempParams, nil
}

// convertOpenAIToolChoiceToAnthropic 将OpenAI的tool_choice转换为Anthropic格式
func convertOpenAIToolChoiceToAnthropic(openaiToolChoice any) *types.ToolChoice {
	if openaiToolChoice == nil {
		return nil
	}

	switch choice := openaiToolChoice.(type) {
	case string:
		// 处理字符串类型："auto", "none", "required"
		switch choice {
		case "auto":
			return &types.ToolChoice{Type: "auto"}
		case "required", "any":
			return &types.ToolChoice{Type: "any"}
		case "none":
			// Anthropic没有"none"选项，返回nil表示不强制使用工具
			return nil
		default:
			// 未知字符串，默认为auto
			return &types.ToolChoice{Type: "auto"}
		}

	case map[string]any:
		// 处理对象类型：{"type": "function", "function": {"name": "tool_name"}}
		if choiceType, ok := choice["type"].(string); ok && choiceType == "function" {
			if functionObj, ok := choice["function"].(map[string]any); ok {
				if name, ok := functionObj["name"].(string); ok {
					return &types.ToolChoice{
						Type: "tool",
						Name: name,
					}
				}
			}
		}
		// 如果无法解析，返回auto
		return &types.ToolChoice{Type: "auto"}

	case types.OpenAIToolChoice:
		// 处理结构化的OpenAIToolChoice类型
		if choice.Type == "function" && choice.Function != nil {
			return &types.ToolChoice{
				Type: "tool",
				Name: choice.Function.Name,
			}
		}
		return &types.ToolChoice{Type: "auto"}

	default:
		// 未知类型，默认为auto
		return &types.ToolChoice{Type: "auto"}
	}
}
