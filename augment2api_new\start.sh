#!/bin/bash

# Augment2Api New 启动脚本

echo "==================================="
echo "    Augment2Api New v2.0.0"
echo "==================================="

# 检查必需的环境变量
check_env() {
    local var_name=$1
    local var_value=$(eval echo \$$var_name)
    
    if [ -z "$var_value" ]; then
        echo "❌ 错误: 环境变量 $var_name 未设置"
        return 1
    else
        echo "✅ $var_name: ${var_value:0:20}..."
        return 0
    fi
}

echo "检查环境变量..."

# 检查必需的环境变量
missing_vars=0

if ! check_env "TENANT_URL"; then
    missing_vars=$((missing_vars + 1))
fi

if ! check_env "ACCESS_TOKEN"; then
    missing_vars=$((missing_vars + 1))
fi

# 检查可选的环境变量
echo ""
echo "可选环境变量:"
echo "AUTH_TOKEN: ${AUTH_TOKEN:-未设置（将使用默认值）}"
echo "PORT: ${PORT:-27080}"
echo "ROUTE_PREFIX: ${ROUTE_PREFIX:-/v1}"
echo "PROXY_URL: ${PROXY_URL:-未设置}"

if [ $missing_vars -gt 0 ]; then
    echo ""
    echo "❌ 缺少 $missing_vars 个必需的环境变量"
    echo ""
    echo "请设置以下环境变量:"
    echo "export TENANT_URL=\"your_augment_tenant_url\""
    echo "export ACCESS_TOKEN=\"your_augment_access_token\""
    echo "export AUTH_TOKEN=\"your_api_auth_token\"  # 可选"
    echo "export PORT=\"27080\"  # 可选"
    echo ""
    echo "然后重新运行此脚本"
    exit 1
fi

echo ""
echo "✅ 环境变量检查通过"
echo ""

# 检查可执行文件是否存在
if [ ! -f "./augment2api_new.exe" ] && [ ! -f "./augment2api_new" ]; then
    echo "❌ 可执行文件不存在，正在编译..."
    go build -o augment2api_new.exe
    if [ $? -ne 0 ]; then
        echo "❌ 编译失败"
        exit 1
    fi
    echo "✅ 编译成功"
fi

# 确定可执行文件名
EXECUTABLE="./augment2api_new.exe"
if [ ! -f "$EXECUTABLE" ]; then
    EXECUTABLE="./augment2api_new"
fi

echo "启动服务器..."
echo "==================================="

# 启动服务器
$EXECUTABLE
