# Augment2Api New Environment Variables Configuration Example
# Copy this file to .env and fill in actual values

# ===== REQUIRED CONFIGURATION =====

# Augment tenant URL (required)
# Example: https://your-tenant.augmentcode.com/api/v1/
TENANT_URL=your_augment_tenant_url

# Augment access token (required)
# Get from Augment console
ACCESS_TOKEN=your_augment_access_token

# ===== OPTIONAL CONFIGURATION =====

# API authentication token (recommended)
# Clients need to provide this token in Authorization header
AUTH_TOKEN=your_api_auth_token

# Service port (default: 27080)
PORT=27080

# Route prefix (default: /v1)
ROUTE_PREFIX=/v1

# Proxy URL (if need to access Augment API through proxy)
# Example: http://proxy.company.com:8080
PROXY_URL=

# Log level (default: info)
# Options: debug, info, warn, error
LOG_LEVEL=info

# User agent string (default: augment2api_new/2.0.0)
USER_AGENT=augment2api_new/2.0.0

# ===== USAGE INSTRUCTIONS =====

# 1. Linux/macOS usage:
#    source .env
#    ./start.sh

# 2. Windows usage:
#    Execute line by line in command prompt:
#    set TENANT_URL=your_augment_tenant_url
#    set ACCESS_TOKEN=your_augment_access_token
#    set AUTH_TOKEN=your_api_auth_token
#    start.bat

# 3. Claude Code connection configuration:
#    export ANTHROPIC_AUTH_TOKEN=your_api_auth_token
#    export ANTHROPIC_BASE_URL=http://localhost:27080
