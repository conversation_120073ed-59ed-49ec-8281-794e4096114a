# Augment2Api New 环境变量配置示例
# 复制此文件为 .env 并填入实际值

# ===== 必需配置 =====

# Augment租户URL（必需）
# 示例: https://your-tenant.augmentcode.com/api/v1/
TENANT_URL=your_augment_tenant_url

# Augment访问令牌（必需）
# 从Augment控制台获取
ACCESS_TOKEN=your_augment_access_token

# ===== 可选配置 =====

# API认证令牌（推荐设置）
# 客户端需要在Authorization头中提供此令牌
AUTH_TOKEN=your_api_auth_token

# 服务端口（默认: 27080）
PORT=27080

# 路由前缀（默认: /v1）
ROUTE_PREFIX=/v1

# 代理URL（如果需要通过代理访问Augment API）
# 示例: http://proxy.company.com:8080
PROXY_URL=

# 日志级别（默认: info）
# 可选值: debug, info, warn, error
LOG_LEVEL=info

# 用户代理字符串（默认: augment2api_new/2.0.0）
USER_AGENT=augment2api_new/2.0.0

# ===== 使用说明 =====

# 1. Linux/macOS 使用方法:
#    source .env
#    ./start.sh

# 2. Windows 使用方法:
#    在命令提示符中逐行执行:
#    set TENANT_URL=your_augment_tenant_url
#    set ACCESS_TOKEN=your_augment_access_token
#    set AUTH_TOKEN=your_api_auth_token
#    start.bat

# 3. Claude Code 连接配置:
#    export ANTHROPIC_AUTH_TOKEN=your_api_auth_token
#    export ANTHROPIC_BASE_URL=http://localhost:27080
