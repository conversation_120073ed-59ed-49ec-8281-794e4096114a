#!/bin/bash

# Augment2Api New API测试脚本

BASE_URL="http://localhost:27080"
AUTH_TOKEN="${AUTH_TOKEN:-123456}"

echo "==================================="
echo "    Augment2Api New API 测试"
echo "==================================="
echo "Base URL: $BASE_URL"
echo "Auth Token: ${AUTH_TOKEN:0:10}..."
echo ""

# 测试健康检查
echo "1. 测试健康检查..."
curl -s "$BASE_URL/health" | jq . 2>/dev/null || curl -s "$BASE_URL/health"
echo -e "\n"

# 测试模型列表
echo "2. 测试模型列表..."
curl -s -H "Authorization: Bearer $AUTH_TOKEN" "$BASE_URL/v1/models" | jq . 2>/dev/null || curl -s -H "Authorization: Bearer $AUTH_TOKEN" "$BASE_URL/v1/models"
echo -e "\n"

# 测试Anthropic API (非流式)
echo "3. 测试Anthropic Messages API (非流式)..."
curl -s -X POST "$BASE_URL/v1/messages" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "max_tokens": 100,
    "messages": [
      {
        "role": "user",
        "content": "Hello! Please respond with just \"Hello from Augment2Api New!\""
      }
    ]
  }' | jq . 2>/dev/null || curl -s -X POST "$BASE_URL/v1/messages" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "max_tokens": 100,
    "messages": [
      {
        "role": "user",
        "content": "Hello! Please respond with just \"Hello from Augment2Api New!\""
      }
    ]
  }'
echo -e "\n"

# 测试OpenAI API (非流式)
echo "4. 测试OpenAI Chat Completions API (非流式)..."
curl -s -X POST "$BASE_URL/v1/chat/completions" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4",
    "messages": [
      {
        "role": "user",
        "content": "Hello! Please respond with just \"Hello from Augment2Api New!\""
      }
    ]
  }' | jq . 2>/dev/null || curl -s -X POST "$BASE_URL/v1/chat/completions" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4",
    "messages": [
      {
        "role": "user",
        "content": "Hello! Please respond with just \"Hello from Augment2Api New!\""
      }
    ]
  }'
echo -e "\n"

# 测试Anthropic API (流式)
echo "5. 测试Anthropic Messages API (流式，前5行)..."
curl -s -X POST "$BASE_URL/v1/messages" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "max_tokens": 50,
    "messages": [
      {
        "role": "user",
        "content": "Count from 1 to 5"
      }
    ],
    "stream": true
  }' | head -5
echo -e "\n"

# 测试OpenAI API (流式)
echo "6. 测试OpenAI Chat Completions API (流式，前5行)..."
curl -s -X POST "$BASE_URL/v1/chat/completions" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4",
    "messages": [
      {
        "role": "user",
        "content": "Count from 1 to 5"
      }
    ],
    "stream": true
  }' | head -5
echo -e "\n"

echo "==================================="
echo "           测试完成"
echo "==================================="
