package config

import (
	"log"
	"os"
)

type Config struct {
	// 服务配置
	Port        string
	AuthToken   string
	RoutePrefix string
	ProxyURL    string
	UserAgent   string

	// Augment API配置
	TenantURL   string
	AccessToken string

	// 其他配置
	LogLevel string
}

const Version = "v2.0.0"

var AppConfig Config

// InitConfig 初始化配置
func InitConfig() error {
	// 从环境变量读取配置
	AppConfig = Config{
		// 服务配置
		Port:        getEnv("PORT", "27080"),
		AuthToken:   getEnv("AUTH_TOKEN", "123456"),
		RoutePrefix: getEnv("ROUTE_PREFIX", ""),
		ProxyURL:    getEnv("PROXY_URL", ""),
		UserAgent:   getEnv("USER_AGENT", "augment2api_new/2.0.0"),

		// Augment API配置
		TenantURL:   getEnv("TENANT_URL", ""),
		AccessToken: getEnv("ACCESS_TOKEN", ""),

		// 其他配置
		LogLevel: getEnv("LOG_LEVEL", "info"),
	}

	log.Printf("Welcome to use Augment2Api New! Current Version: %s", Version)

	log.Printf("Augment2Api配置加载完成:\n"+
		"----------------------------------------\n"+
		"Port:         %s\n"+
		"AuthToken:    %s\n"+
		"RoutePrefix:  %s\n"+
		"ProxyURL:     %s\n"+
		"TenantURL:    %s\n"+
		"AccessToken:  %s\n"+
		"----------------------------------------",
		AppConfig.Port,
		AppConfig.AuthToken,
		AppConfig.RoutePrefix,
		AppConfig.ProxyURL,
		AppConfig.TenantURL,
		maskToken(AppConfig.AccessToken))

	log.Println("Everything is set up, now start to fully enjoy the charm of AI!")

	return nil
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// maskToken 遮蔽token显示
func maskToken(token string) string {
	if len(token) <= 8 {
		return "****"
	}
	return token[:4] + "****" + token[len(token)-4:]
}

// ModelMap 模型映射表
var ModelMap = map[string]string{
	"claude-3-5-sonnet-20241022": "claude-3.7-agent",
	"claude-3-5-haiku-20241022":  "augment-chat",
	"claude-3-opus-20240229":     "claude-3.7-agent",
	"claude-3-sonnet-20240229":   "claude-3.7-agent",
	"claude-3-haiku-20240307":    "augment-chat",
	"gpt-4":                      "claude-3.7-agent",
	"gpt-4-turbo":                "claude-3.7-agent",
	"gpt-3.5-turbo":              "augment-chat",
}
