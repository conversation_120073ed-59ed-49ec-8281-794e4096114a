# Augment2Api New 部署指南

## 项目完成状态

✅ **项目已完成** - 基于kiro2api架构的完整实现

### 已实现功能

- ✅ 完整的类型定义系统 (types/)
- ✅ 双向格式转换器 (converter/)
- ✅ HTTP服务器和处理器 (server/)
- ✅ 智能HTTP客户端 (utils/)
- ✅ 配置管理系统 (config/)
- ✅ Anthropic Claude API兼容 (/v1/messages)
- ✅ OpenAI Chat Completions API兼容 (/v1/chat/completions)
- ✅ 流式和非流式响应支持
- ✅ 工具调用支持
- ✅ 模型列表API (/v1/models)
- ✅ 认证中间件
- ✅ CORS支持
- ✅ 编译成功的可执行文件

## 快速部署

### 1. 环境准备

```bash
# 必需环境变量
export TENANT_URL="https://your-tenant.augmentcode.com/api/v1/"
export ACCESS_TOKEN="your_augment_access_token"

# 推荐设置
export AUTH_TOKEN="your_api_auth_token"
export PORT="27080"
```

### 2. 启动服务

**Linux/macOS:**
```bash
./start.sh
```

**Windows:**
```cmd
start.bat
```

**手动启动:**
```bash
./augment2api_new.exe
```

### 3. 验证部署

```bash
# 健康检查
curl http://localhost:27080/health

# 运行完整测试
./test_api.sh
```

## Claude Code 集成

设置环境变量后，Claude Code可以直接连接：

```bash
export ANTHROPIC_AUTH_TOKEN=your_api_auth_token
export ANTHROPIC_BASE_URL=http://localhost:27080
```

## 架构对比

### 原始 augment2api
```
Client → augment2api → Augment API
```

### 新版 augment2api_new (基于kiro2api)
```
Client → augment2api_new → Augment API → augment2api_new → Client
```

### 支持的API格式

1. **Anthropic Claude API** (`/v1/messages`)
   - 完全兼容Anthropic官方API格式
   - 支持流式和非流式响应
   - 支持工具调用

2. **OpenAI Chat Completions API** (`/v1/chat/completions`)
   - 完全兼容OpenAI官方API格式
   - 通过格式转换实现
   - 支持流式和非流式响应

## 技术特性

### 智能请求处理
- 基于请求复杂度的超时管理
- 工具调用检测和处理
- 消息长度分析

### 格式转换
- Anthropic ↔ OpenAI ↔ Augment 三向转换
- 保持API语义一致性
- 工具调用格式适配

### 性能优化
- HTTP连接池复用
- 智能客户端选择
- 流式响应处理

## 文件结构

```
augment2api_new/
├── types/              # 类型定义
│   ├── anthropic.go    # Anthropic API类型
│   ├── openai.go       # OpenAI API类型
│   ├── augment.go      # Augment API类型
│   └── common.go       # 通用类型
├── converter/          # 格式转换器
│   └── converter.go    # 核心转换逻辑
├── server/             # HTTP服务器
│   └── handlers.go     # 请求处理器
├── utils/              # 工具函数
│   └── client.go       # HTTP客户端
├── config/             # 配置管理
│   └── config.go       # 配置定义
├── main.go             # 主程序入口
├── go.mod              # Go模块定义
├── go.sum              # 依赖锁定
├── README.md           # 使用说明
├── DEPLOYMENT.md       # 部署指南
├── .env.example        # 环境变量示例
├── start.sh            # Linux/macOS启动脚本
├── start.bat           # Windows启动脚本
├── test_api.sh         # API测试脚本
└── augment2api_new.exe # 编译后的可执行文件
```

## 故障排除

### 常见问题

1. **编译错误**
   ```bash
   go mod tidy
   go build -o augment2api_new.exe
   ```

2. **环境变量未设置**
   - 检查 TENANT_URL 和 ACCESS_TOKEN
   - 参考 .env.example 文件

3. **端口冲突**
   - 修改 PORT 环境变量
   - 默认端口: 27080

4. **认证失败**
   - 检查 AUTH_TOKEN 设置
   - 确保客户端使用正确的Bearer token

### 日志分析

程序启动时会显示：
- 配置加载状态
- 服务端口信息
- API端点地址

运行时会记录：
- 请求处理过程
- 错误信息
- 性能指标

## 生产环境建议

1. **安全性**
   - 设置强密码的 AUTH_TOKEN
   - 使用HTTPS反向代理
   - 限制访问IP范围

2. **性能**
   - 调整超时参数
   - 监控内存使用
   - 配置日志轮转

3. **监控**
   - 健康检查端点: `/health`
   - 监控响应时间
   - 跟踪错误率

## 版本信息

- **版本**: v2.0.0
- **基于**: kiro2api架构
- **Go版本**: 1.21+
- **编译状态**: ✅ 成功
- **测试状态**: ✅ 就绪
