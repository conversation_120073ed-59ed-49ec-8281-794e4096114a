# Redis 连接配置
# 格式: redis://default:password@host:port
# 必填项
REDIS_CONN_STRING=redis://default:your_secure_password@redis:6379


# 面板访问密码
# 为了安全，此配置必填！
ACCESS_PWD=your_secure_access_password


# API 认证令牌
# 如果设置，则所有 API 请求需要在 Authorization 头中提供此令牌
# 格式: 随机数字+字母组合即可
# 可选项，如果不设置则不启用认证
AUTH_TOKEN=your_secure_auth_token


# API 请求前缀
# 可自定义，默认为空
ROUTE_PREFIX=your_api_prefix

# 调试模式
# 设置为 true 时启用更详细的日志输出
# 可选项，默认为 false
DEBUG=false

# 编码模式配置
# 仅用于开发和调试
# 可选项，默认为 false
CODING_MODE=false
CODING_TOKEN=
TENANT_URL=