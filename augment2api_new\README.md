# Augment2Api New

基于kiro2api架构的Augment API兼容层，支持Anthropic Claude API和OpenAI API格式。

## 功能特性

- ✅ 完全兼容Anthropic Claude API (`/v1/messages`)
- ✅ 完全兼容OpenAI Chat Completions API (`/v1/chat/completions`)
- ✅ 支持流式和非流式响应
- ✅ 支持工具调用（Tool Calling）
- ✅ 智能请求复杂度分析和超时管理
- ✅ 模型列表API (`/v1/models`)
- ✅ CORS支持
- ✅ 认证中间件

## 快速开始

### 1. 环境变量配置

```bash
# 必需配置
export TENANT_URL="your_augment_tenant_url"     # Augment租户URL
export ACCESS_TOKEN="your_augment_access_token" # Augment访问令牌

# 可选配置
export AUTH_TOKEN="your_api_auth_token"         # API认证令牌（推荐设置）
export PORT="27080"                             # 服务端口，默认27080
export ROUTE_PREFIX="/v1"                       # 路由前缀，默认/v1
export PROXY_URL=""                             # 代理URL（如需要）
export LOG_LEVEL="info"                         # 日志级别
```

### 2. 编译和运行

**Linux/macOS:**
```bash
# 进入项目目录
cd augment2api_new

# 下载依赖
go mod tidy

# 编译
go build -o augment2api_new

# 运行
./start.sh
```

**Windows:**
```cmd
# 进入项目目录
cd augment2api_new

# 下载依赖
go mod tidy

# 编译
go build -o augment2api_new.exe

# 设置环境变量
set TENANT_URL=your_augment_tenant_url
set ACCESS_TOKEN=your_augment_access_token
set AUTH_TOKEN=your_api_auth_token

# 运行
start.bat
```

**或者使用交互式设置脚本 (Windows):**
```cmd
setup.bat
```

### 3. 使用Claude Code连接

```bash
# 设置环境变量
export ANTHROPIC_AUTH_TOKEN=your_api_auth_token
export ANTHROPIC_BASE_URL=http://localhost:27080

# 现在可以使用Claude Code了
```

## API接口

### Anthropic Claude API

```bash
# 非流式请求
curl -X POST http://localhost:27080/v1/messages \
  -H "Authorization: Bearer your_api_auth_token" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "max_tokens": 1024,
    "messages": [
      {
        "role": "user",
        "content": "Hello, Claude!"
      }
    ]
  }'

# 流式请求
curl -X POST http://localhost:27080/v1/messages \
  -H "Authorization: Bearer your_api_auth_token" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "max_tokens": 1024,
    "messages": [
      {
        "role": "user",
        "content": "Hello, Claude!"
      }
    ],
    "stream": true
  }'
```

### OpenAI Chat Completions API

```bash
# 非流式请求
curl -X POST http://localhost:27080/v1/chat/completions \
  -H "Authorization: Bearer your_api_auth_token" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4",
    "messages": [
      {
        "role": "user",
        "content": "Hello, GPT!"
      }
    ]
  }'

# 流式请求
curl -X POST http://localhost:27080/v1/chat/completions \
  -H "Authorization: Bearer your_api_auth_token" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4",
    "messages": [
      {
        "role": "user",
        "content": "Hello, GPT!"
      }
    ],
    "stream": true
  }'
```

### 模型列表

```bash
curl -X GET http://localhost:27080/v1/models \
  -H "Authorization: Bearer your_api_auth_token"
```

## 支持的模型

| 模型名称 | 映射到Augment模型 |
|---------|------------------|
| claude-3-5-sonnet-20241022 | claude-3.7-agent |
| claude-3-5-haiku-20241022 | augment-chat |
| claude-3-opus-20240229 | claude-3.7-agent |
| claude-3-sonnet-20240229 | claude-3.7-agent |
| claude-3-haiku-20240307 | augment-chat |
| gpt-4 | claude-3.7-agent |
| gpt-4-turbo | claude-3.7-agent |
| gpt-3.5-turbo | augment-chat |

## 架构说明

本项目完全基于kiro2api的架构设计，实现了以下转换流程：

```
Client → augment2api_new → Augment API → augment2api_new → Client
```

### 核心组件

- **types/**: 类型定义（Anthropic、OpenAI、Augment格式）
- **converter/**: 格式转换器（Anthropic ↔ OpenAI ↔ Augment）
- **server/**: HTTP处理器和路由
- **utils/**: 工具函数和HTTP客户端
- **config/**: 配置管理

### 转换流程

1. **Anthropic API请求** → `AnthropicRequest` → `AugmentRequest` → Augment API
2. **Augment API响应** → `AugmentResponse` → `AnthropicResponse` → 客户端
3. **OpenAI API请求** → `OpenAIRequest` → `AnthropicRequest` → `AugmentRequest` → Augment API
4. **Augment API响应** → `AugmentResponse` → `AnthropicResponse` → `OpenAIResponse` → 客户端

## 健康检查

```bash
curl http://localhost:27080/health
```

## 故障排除

### 常见问题

1. **无可用Token错误**
   - 检查 `TENANT_URL` 和 `ACCESS_TOKEN` 环境变量是否正确设置

2. **认证失败**
   - 检查 `AUTH_TOKEN` 是否正确设置
   - 确保请求头包含正确的 `Authorization: Bearer token`

3. **连接超时**
   - 检查网络连接
   - 如需要代理，设置 `PROXY_URL` 环境变量

### 日志查看

程序会输出详细的日志信息，包括：
- 配置加载状态
- 请求处理过程
- 错误信息

## 版本信息

- 版本: v2.0.0
- 基于: kiro2api架构
- Go版本: 1.21+

## 许可证

本项目遵循原augment2api项目的许可证。
