package types

// AugmentRequest Augment API请求结构
type AugmentRequest struct {
	ChatHistory    []AugmentChatHistory `json:"chat_history"`
	Message        string               `json:"message"`
	AgentMemories  string               `json:"agent_memories"`
	Mode           string               `json:"mode"`
	Prefix         string               `json:"prefix"`
	Suffix         string               `json:"suffix"`
	Lang           string               `json:"lang"`
	Path           string               `json:"path"`
	UserGuideLines string               `json:"user_guidelines"`
	Blobs          struct {
		CheckpointID string        `json:"checkpoint_id"`
		AddedBlobs   []interface{} `json:"added_blobs"`
		DeletedBlobs []interface{} `json:"deleted_blobs"`
	} `json:"blobs"`
	UserGuidedBlobs       []interface{}    `json:"user_guided_blobs"`
	ExternalSourceIds     []interface{}    `json:"external_source_ids"`
	FeatureDetectionFlags struct {
		SupportRawOutput bool `json:"support_raw_output"`
	} `json:"feature_detection_flags"`
	ToolDefinitions []ToolDefinition `json:"tool_definitions"`
	Nodes           []Node           `json:"nodes"`
}

// AugmentChatHistory Augment聊天历史结构
type AugmentChatHistory struct {
	ResponseText   string `json:"response_text"`
	RequestMessage string `json:"request_message"`
	RequestID      string `json:"request_id"`
	RequestNodes   []Node `json:"request_nodes"`
	ResponseNodes  []Node `json:"response_nodes"`
}

// AugmentResponse Augment API响应结构
type AugmentResponse struct {
	Text string `json:"text"`
	Done bool   `json:"done"`
}

// ToolDefinition 工具定义结构
type ToolDefinition struct {
	Name            string `json:"name"`
	Description     string `json:"description"`
	InputSchemaJSON string `json:"input_schema_json"`
	ToolSafety      int    `json:"tool_safety"`
}

// Node 节点结构
type Node struct {
	ID          int         `json:"id"`
	Type        int         `json:"type"`
	Content     string      `json:"content"`
	ToolUse     ToolUse     `json:"tool_use"`
	AgentMemory AgentMemory `json:"agent_memory"`
}

// ToolUse 工具使用结构
type ToolUse struct {
	ToolUseID string `json:"tool_use_id"`
	ToolName  string `json:"tool_name"`
	InputJSON string `json:"input_json"`
}

// AgentMemory 代理记忆结构
type AgentMemory struct {
	Content string `json:"content"`
}

// ModelObject 模型对象结构
type ModelObject struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int    `json:"created"`
	OwnedBy string `json:"owned_by"`
}

// ModelsResponse 模型列表响应结构
type ModelsResponse struct {
	Object string        `json:"object"`
	Data   []ModelObject `json:"data"`
}
