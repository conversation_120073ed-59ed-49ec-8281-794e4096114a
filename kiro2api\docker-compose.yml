services:
  kiro2api:
    image: ghcr.io/caidaoli/kiro2api:latest
    container_name: kiro2api
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - AWS_REFRESHTOKEN=${AWS_REFRESHTOKEN}
      - KIRO_CLIENT_TOKEN=${KIRO_CLIENT_TOKEN:-123456}
      - PORT=${PORT:-8080}
      - GIN_MODE=${GIN_MODE:-release}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - LOG_FORMAT=${LOG_FORMAT:-json}
    volumes:
      - aws_sso_cache:/home/<USER>/.aws/sso/cache
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  aws_sso_cache:
