# Windows Usage Guide

## Quick Start for Windows Users

### Method 1: Interactive Setup (Recommended)

1. Open Command Prompt (cmd)
2. Navigate to the project directory:
   ```cmd
   cd augment2api_new
   ```
3. Run the interactive setup:
   ```cmd
   setup.bat
   ```
4. Follow the prompts to enter your configuration

### Method 2: Manual Setup

1. Open Command Prompt (cmd)
2. Navigate to the project directory:
   ```cmd
   cd augment2api_new
   ```
3. Set environment variables:
   ```cmd
   set TENANT_URL=https://your-tenant.augmentcode.com/api/v1/
   set ACCESS_TOKEN=your_augment_access_token
   set AUTH_TOKEN=your_api_auth_token
   set PORT=27080
   ```
4. Start the server:
   ```cmd
   start.bat
   ```

### Method 3: PowerShell

1. Open PowerShell
2. Navigate to the project directory:
   ```powershell
   cd augment2api_new
   ```
3. Set environment variables:
   ```powershell
   $env:TENANT_URL="https://your-tenant.augmentcode.com/api/v1/"
   $env:ACCESS_TOKEN="your_augment_access_token"
   $env:AUTH_TOKEN="your_api_auth_token"
   $env:PORT="27080"
   ```
4. Start the server:
   ```powershell
   .\augment2api_new.exe
   ```

## Configuration for Claude Code

After starting the server, configure Claude Code:

1. Open Command Prompt or PowerShell
2. Set Claude Code environment variables:
   ```cmd
   set ANTHROPIC_AUTH_TOKEN=your_api_auth_token
   set ANTHROPIC_BASE_URL=http://localhost:27080
   ```
   
   Or in PowerShell:
   ```powershell
   $env:ANTHROPIC_AUTH_TOKEN="your_api_auth_token"
   $env:ANTHROPIC_BASE_URL="http://localhost:27080"
   ```

3. Start Claude Code and it should connect automatically

## Testing the Installation

Open a new Command Prompt and test the API:

```cmd
curl -X GET http://localhost:27080/health
```

If curl is not available, you can test in a web browser by visiting:
```
http://localhost:27080/health
```

## Troubleshooting

### Common Issues

1. **"go is not recognized"**
   - Install Go from https://golang.org/dl/
   - Make sure Go is in your PATH

2. **"curl is not recognized"**
   - Use PowerShell instead of cmd (PowerShell has curl built-in)
   - Or install curl for Windows

3. **Port already in use**
   - Change the PORT environment variable:
     ```cmd
     set PORT=27081
     ```

4. **Environment variables not persisting**
   - Environment variables set in cmd only last for that session
   - Use setup.bat for each new session
   - Or set system environment variables through Windows Settings

### Setting Permanent Environment Variables

1. Open Windows Settings
2. Search for "Environment Variables"
3. Click "Edit the system environment variables"
4. Click "Environment Variables..." button
5. Add the required variables in "User variables" section:
   - TENANT_URL
   - ACCESS_TOKEN
   - AUTH_TOKEN (optional)
   - PORT (optional)

## File Descriptions

- `setup.bat` - Interactive setup script
- `start.bat` - Server startup script
- `test.bat` - Simple test script
- `augment2api_new.exe` - Main executable
- `.env.example` - Environment variables template

## Support

If you encounter issues:

1. Check that all required environment variables are set
2. Verify the executable file exists and is not corrupted
3. Check Windows Defender or antivirus isn't blocking the executable
4. Try running as Administrator if permission issues occur
